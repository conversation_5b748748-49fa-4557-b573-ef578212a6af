# Selection Direction and Crash Fix

## Issues Fixed

### 1. Selection Direction Preservation
**Problem**: Selections made from bottom-to-top or right-to-left were not preserving their original direction, causing unexpected behavior.

**Solution**: Updated `expandSelectionForMergedCells()` to detect and preserve the original selection direction:

```typescript
// Preserve the original selection direction
const isBottomToTop = originalStartRow > originalEndRow;
const isRightToLeft = originalStartCol > originalEndCol;

return {
  start: { 
    row: isBottomToTop ? maxLogicalRow : minLogicalRow, 
    col: isRightToLeft ? maxLogicalCol : minLogicalCol 
  },
  end: { 
    row: isBottomToTop ? minLogicalRow : maxLogicalRow, 
    col: isRightToLeft ? minLogicalCol : maxLogicalCol 
  }
};
```

### 2. Crash Prevention for Single Cell Selection After Colspan
**Problem**: Clicking on single cells that come after a colspan cell could cause crashes because the selection logic wasn't consistently applying expansion to all selection operations.

**Root Cause**: Some selection operations (single cell clicks, double-clicks, keyboard navigation) were setting selections directly without going through the `expandSelectionForMergedCells()` function, which could result in invalid logical positions.

**Solution**: Ensured ALL selection operations go through expansion logic:

#### Updated `handleCellSelection()`:
```typescript
// Always expand selection to handle merged cells properly
const expandedSelection = expandSelectionForMergedCells(
  tableProperties.cells,
  rawSelection,
  tableProperties.rows,
  tableProperties.columns
);
setSelectedCells(expandedSelection);
```

#### Updated `handleDoubleClick()`:
- Now applies expansion to single cell selections when starting to edit
- Ensures consistent behavior between click and double-click

#### Updated Keyboard Navigation:
- Single cell moves now also go through expansion
- Prevents crashes when navigating to cells after merged cells

#### Enhanced Error Handling:
- Added comprehensive validation in `cellToLogicalPosition()`
- Enhanced `createLogicalGrid()` with bounds checking
- Added fallback values for edge cases

## Key Improvements

### 1. Robust Error Handling
```typescript
// Validate input parameters
if (!cells || cells.length === 0 || rowIndex < 0 || rowIndex >= cells.length) {
  console.warn(`Invalid rowIndex ${rowIndex} for table with ${cells?.length || 0} rows`);
  return { logicalRow: Math.max(0, rowIndex), logicalCol: 0 };
}
```

### 2. Safe Array Access
```typescript
// Ensure cell has valid span values
const cellRowspan = Math.max(1, cell.rowspan || 1);
const cellColspan = Math.max(1, cell.colspan || 1);

// Check bounds before accessing grid
if (grid[r] && c < grid[r].length) {
  grid[r][c] = { rowIndex, colIndex };
}
```

### 3. Direction-Aware Selection
The selection system now correctly handles all four selection directions:
- ↘️ Top-left to bottom-right (normal)
- ↖️ Bottom-right to top-left 
- ↙️ Top-right to bottom-left
- ↗️ Bottom-left to top-right

## Testing

Added comprehensive tests covering:
- ✅ Selection direction preservation for all directions
- ✅ Crash prevention with problematic table structures
- ✅ Edge cases with cells after colspan
- ✅ Validation of logical position calculations

All 13 tests pass successfully.

## User Impact

### Before the Fix:
- ❌ Selections from bottom-to-top would flip direction unexpectedly
- ❌ **Clicking single cells after colspan could crash the application**
- ❌ Inconsistent selection behavior depending on drag direction
- ❌ Some selection operations bypassed expansion logic

### After the Fix:
- ✅ All selection directions work consistently and preserve their direction
- ✅ **No crashes when clicking any single cell in complex table structures**
- ✅ **ALL selection operations (click, double-click, keyboard) go through expansion**
- ✅ Predictable, intuitive selection behavior regardless of drag direction
- ✅ Robust error handling prevents edge case failures
- ✅ Consistent behavior between different selection methods

## Files Modified
- `src/utils/tableUtils.ts` - Enhanced error handling and direction preservation
- `src/utils/__tests__/tableUtils.test.ts` - Added comprehensive tests
- `SELECTION_DIRECTION_AND_CRASH_FIX.md` - This documentation

The table editor now provides a rock-solid, crash-free experience with intuitive selection behavior that works consistently in all directions and with all table structures.
