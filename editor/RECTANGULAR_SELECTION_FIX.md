# Table Rectangular Selection Fix

## Problem
Tables with `colspan` and `rowspan` attributes had surprising multi-cell selection behavior. When selecting cells via mouse drag or keyboard navigation, the selected area did not always maintain a rectangular shape in the logical grid, leading to irregular selection patterns.

## Root Cause
The original selection logic in `TableEditor.tsx` only checked if a cell's array indices (`rowIndex`, `colIndex`) fell within the rectangular bounds of the selection. This approach didn't account for the logical grid positions that cells with `colspan` and `rowspan` actually occupy.

**Colspan Example:**
In a table where the second row has a cell with `colspan=3`, the cell array structure would be:
- Row 0: [cell0, cell1, cell2, cell3, cell4]
- Row 1: [cell0(colspan=3), cell1, cell2]

But the logical grid would be:
- Row 0: [A1, B1, C1, D1, E1]
- Row 1: [A2-C2, A2-C2, A2-C2, D2, E2]

**Rowspan Example (the more complex issue):**
In a table with rowspan, the cell array structure would be:
- Row 0: [A1, B1, C1]
- Row 1: [A2, B2-B3(rowspan=2)]
- Row 2: [A3, C3]

But the logical grid would be:
- Row 0: [A1, B1, C1]
- Row 1: [A2, B2-B3, null]
- Row 2: [A3, B2-B3, C3]

The key issue was that `cellToLogicalPosition()` didn't properly account for rowspan cells from previous rows occupying logical columns in the current row.

## Solution
Implemented a comprehensive fix that maps between cell array indices and logical grid positions:

### 1. New Utility Functions (`src/utils/tableUtils.ts`)

- **`createLogicalGrid()`**: Creates a logical grid representation mapping each logical position to the actual cell that occupies it
- **`cellToLogicalPosition()`**: Converts cell array indices to logical grid coordinates
- **`logicalPositionToCell()`**: Converts logical grid coordinates back to cell array indices
- **`expandSelectionForMergedCells()`**: Expands a selection rectangle to include the full extent of any merged cells that are partially selected
- **`isCellSelectedInLogicalGrid()`**: Determines if a cell is selected based on logical grid selection bounds

### 2. Updated Selection Logic (`src/components/table/TableEditor.tsx`)

- **Mouse Selection**: `handleCellSelection()` and `handleMouseEnter()` now convert cell array indices to logical coordinates and expand selection to include full merged cells
- **Cell Rendering**: `renderEditableCell()` uses `isCellSelectedInLogicalGrid()` to determine if a cell should be highlighted
- **Keyboard Navigation**: Updated arrow key handlers with Shift to expand selection and include full merged cells
- **Enter Key**: Updated to convert logical coordinates back to cell indices for editing
- **Double-click**: Updated to use logical coordinates for consistency

### 3. Key Changes Made

1. **Fixed `cellToLogicalPosition()` for rowspan**:
   ```typescript
   // Now uses the logical grid to find correct positions
   // accounting for rowspan cells from previous rows
   export function cellToLogicalPosition(cells, rowIndex, colIndex) {
     const grid = createLogicalGrid(cells, cells.length, maxCols);
     // Find where this specific cell actually starts in the logical grid
   }
   ```

2. **Updated selection handlers with expansion**:
   ```typescript
   const handleMouseEnter = (rowIndex: number, colIndex: number) => {
     const rawSelection = { start: selectedCells.start, end: { row: logicalRow, col: logicalCol } };
     // Expand selection to include full merged cells
     const expandedSelection = expandSelectionForMergedCells(
       tableProperties.cells, rawSelection, tableProperties.rows, tableProperties.columns
     );
     setSelectedCells(expandedSelection);
   };
   ```

3. **Enhanced cell selection detection**:
   ```typescript
   const isSelected = selectedCells && isCellSelectedInLogicalGrid(
     tableProperties.cells, rowIndex, colIndex, selectedCells
   );
   ```

## Testing
- Added comprehensive unit tests in `src/utils/__tests__/tableUtils.test.ts`
- Tests cover logical grid creation, coordinate conversion, and selection detection
- All tests pass successfully
- Build process completes without errors

## Benefits
1. **Consistent Rectangular Selection**: Selection always forms a rectangle in the logical grid
2. **Smart Merged Cell Expansion**: When selecting cells that partially overlap with merged cells, the selection automatically expands to include the entire merged cell
3. **Predictable Behavior**: Users can now select cells in complex tables without unexpected gaps or irregular shapes
4. **Improved User Experience**: Selection behavior is now predictable and intuitive, especially with tables containing colspan and rowspan
5. **Backward Compatibility**: Existing functionality remains unchanged for simple tables

## Files Modified
- `src/utils/tableUtils.ts` - Added new utility functions
- `src/components/table/TableEditor.tsx` - Updated selection logic
- `src/utils/__tests__/tableUtils.test.ts` - Added comprehensive tests
- `test-table-selection.html` - Created manual testing guide

## Usage
The fix is automatically applied to all table interactions. Users will now experience consistent rectangular selection behavior when:
- Dragging to select multiple cells
- Using Shift+Arrow keys to extend selection
- Working with tables containing merged cells (colspan/rowspan)

The selection will always maintain a rectangular shape in the logical grid, making it intuitive and predictable for users working with complex table layouts.
