<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Selection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .instructions {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        table {
            border-collapse: collapse;
            margin: 20px 0;
        }
        td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
            min-width: 60px;
            height: 40px;
        }
        .highlight {
            background-color: rgba(0, 120, 212, 0.3);
        }
    </style>
</head>
<body>
    <h1>Table Selection Test</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Open the editor application</li>
            <li>Create a new table or use the provided table.example.html</li>
            <li>Try selecting cells by dragging across the table</li>
            <li>Verify that the selection always forms a rectangular shape in the logical grid</li>
            <li>Test with tables that have colspan and rowspan attributes</li>
        </ol>
        
        <h4>Expected Behavior:</h4>
        <ul>
            <li>When dragging to select cells, the selection should always form a rectangle</li>
            <li>Cells with colspan/rowspan should be included if any part overlaps the selection rectangle</li>
            <li>The selection should not have irregular shapes due to merged cells</li>
        </ul>
    </div>

    <div class="test-container">
        <h3>Example Table Structure (from table.example.html):</h3>
        <p>This shows the logical structure of the complex table. The actual selection behavior should be tested in the editor.</p>
        
        <table>
            <tr>
                <td>A1</td>
                <td>B1</td>
                <td>C1</td>
                <td>D1</td>
                <td>E1</td>
            </tr>
            <tr>
                <td colspan="3">A2-C2 (colspan=3)</td>
                <td>D2</td>
                <td>E2</td>
            </tr>
            <tr>
                <td>A3</td>
                <td>B3</td>
                <td colspan="2">C3-D3 (colspan=2)</td>
                <td rowspan="2">E3-E4 (rowspan=2)</td>
            </tr>
            <tr>
                <td>A4</td>
                <td>B4</td>
                <td>C4</td>
                <td>D4</td>
            </tr>
        </table>
    </div>

    <div class="test-container">
        <h3>Test Scenarios:</h3>
        <ol>
            <li><strong>Simple Rectangle:</strong> Select from A1 to C2 - should include A1, B1, C1, and the merged A2-C2 cell</li>
            <li><strong>Overlapping Merged Cells:</strong> Select from B3 to D4 - should include all cells that overlap this logical rectangle</li>
            <li><strong>Rowspan Selection:</strong> Select from D3 to E4 - should include the rowspan cell E3-E4</li>
            <li><strong>Keyboard Navigation:</strong> Use arrow keys with Shift to extend selection - should maintain rectangular shape</li>
        </ol>
    </div>

    <div class="test-container">
        <h3>Implementation Details:</h3>
        <p>The fix implements the following changes:</p>
        <ul>
            <li>Added utility functions to map between cell array indices and logical grid positions</li>
            <li>Updated selection logic to work with logical coordinates instead of array indices</li>
            <li>Modified cell selection detection to account for colspan and rowspan</li>
            <li>Ensured keyboard navigation works with the logical grid</li>
        </ul>
    </div>

    <script>
        // Simple demonstration of the logical grid concept
        console.log('Table Selection Test Page Loaded');
        console.log('Open the editor application to test the actual selection behavior');
    </script>
</body>
</html>
