# Rowspan Selection Fix Summary

## Problem Identified
You were absolutely right! The rowspan handling was still causing weird selections. The issue was specifically in the `cellToLogicalPosition()` function.

## Root Cause
The original `cellToLogicalPosition()` function only considered colspans within the current row, but didn't account for rowspan cells from previous rows that occupy logical columns in the current row.

### Example of the Problem
Consider this table structure:
```
Row 0: [A1, B1, C1]
Row 1: [A2, B2-B3(rowspan=2)]  // B2-B3 spans down to row 2
Row 2: [A3, C3]                // C3 is actually at logical column 2, not 1
```

**Logical Grid:**
```
Row 0: [A1, B1, C1]
Row 1: [A2, B2-B3, null]
Row 2: [A3, B2-B3, C3]
```

The old `cellToLogicalPosition()` would incorrectly calculate:
- C3 cell (row 2, array index 1) → logical position (2, 1) ❌

But it should be:
- C3 cell (row 2, array index 1) → logical position (2, 2) ✅

## Solution Implemented

### 1. Fixed `cellToLogicalPosition()` Function
```typescript
export function cellToLogicalPosition(cells, rowIndex, colIndex) {
  // Create a logical grid to find the correct position
  const grid = createLogicalGrid(cells, cells.length, maxCols);
  
  // Find the logical position where this specific cell starts
  for (let logicalCol = 0; logicalCol < maxCols; logicalCol++) {
    const cellRef = grid[rowIndex][logicalCol];
    if (cellRef && cellRef.rowIndex === rowIndex && cellRef.colIndex === colIndex) {
      return { logicalRow, logicalCol };
    }
  }
}
```

### 2. Enhanced Selection Expansion
The `expandSelectionForMergedCells()` function now works correctly because it receives accurate logical positions from the fixed `cellToLogicalPosition()` function.

### 3. Comprehensive Testing
Added specific tests for rowspan scenarios:
- Simple rowspan table structure
- Complex tables with both colspan and rowspan
- The exact user scenario: selecting cells and expanding across rowspan

## Test Results
All 11 tests now pass, including:
- ✅ Basic logical grid creation with rowspan
- ✅ Correct cell-to-logical-position conversion
- ✅ Proper selection expansion for rowspan cells
- ✅ User's specific scenario: maintaining rectangular selection across rowspan

## User Impact
Now when you:
1. Select multiple cells in a row
2. Expand the selection to a row with rowspan cells
3. The originally selected cells **remain selected** ✅
4. The selection properly expands to include the full rowspan cell ✅
5. The final selection always forms a perfect rectangle in the logical grid ✅

## Files Modified
- `src/utils/tableUtils.ts` - Fixed `cellToLogicalPosition()` function
- `src/utils/__tests__/tableUtils.test.ts` - Added comprehensive rowspan tests
- `RECTANGULAR_SELECTION_FIX.md` - Updated documentation

The fix ensures that both colspan AND rowspan selections now work perfectly, maintaining rectangular shapes and predictable behavior for users working with complex table layouts.
