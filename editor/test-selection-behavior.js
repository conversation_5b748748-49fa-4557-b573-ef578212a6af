// Test script to demonstrate the improved table selection behavior
// This can be run in the browser console when testing the editor

console.log('Testing Table Selection Behavior');

// Test data representing the table structure from table.example.html
const testTable = [
  // Row 0: [A1, B1, C1, D1, E1]
  [
    { content: 'A1', colspan: 1, rowspan: 1 },
    { content: 'B1', colspan: 1, rowspan: 1 },
    { content: 'C1', colspan: 1, rowspan: 1 },
    { content: 'D1', colspan: 1, rowspan: 1 },
    { content: 'E1', colspan: 1, rowspan: 1 }
  ],
  // Row 1: [A2-C2 (colspan=3), D2, E2]
  [
    { content: 'A2-C2', colspan: 3, rowspan: 1 },
    { content: 'D2', colspan: 1, rowspan: 1 },
    { content: 'E2', colspan: 1, rowspan: 1 }
  ],
  // Row 2: [A3, B3, C3-D3 (colspan=2), E3-E4 (rowspan=2)]
  [
    { content: 'A3', colspan: 1, rowspan: 1 },
    { content: 'B3', colspan: 1, rowspan: 1 },
    { content: 'C3-D3', colspan: 2, rowspan: 1 },
    { content: 'E3-E4', colspan: 1, rowspan: 2 }
  ],
  // Row 3: [A4, B4, C4, D4]
  [
    { content: 'A4', colspan: 1, rowspan: 1 },
    { content: 'B4', colspan: 1, rowspan: 1 },
    { content: 'C4', colspan: 1, rowspan: 1 },
    { content: 'D4', colspan: 1, rowspan: 1 }
  ]
];

// Test scenarios to verify the fix
const testScenarios = [
  {
    name: 'Select first 3 cells of row 1, then expand to row 2',
    description: 'Should maintain selection of A1, B1, C1 when expanding to include A2-C2 merged cell',
    initialSelection: { start: { row: 0, col: 0 }, end: { row: 0, col: 2 } }, // A1 to C1
    expandedSelection: { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } }, // Expand to include part of A2-C2
    expectedResult: { start: { row: 0, col: 0 }, end: { row: 1, col: 2 } } // Should expand to include full A2-C2
  },
  {
    name: 'Select across rowspan cell',
    description: 'Should expand to include full E3-E4 rowspan cell',
    initialSelection: { start: { row: 2, col: 3 }, end: { row: 2, col: 4 } }, // D3 to E3
    expectedResult: { start: { row: 2, col: 2 }, end: { row: 3, col: 4 } } // Should include C3-D3 and E3-E4
  },
  {
    name: 'Simple selection without merged cells',
    description: 'Should remain unchanged when no merged cells are involved',
    initialSelection: { start: { row: 0, col: 0 }, end: { row: 0, col: 1 } }, // A1 to B1
    expectedResult: { start: { row: 0, col: 0 }, end: { row: 0, col: 1 } } // Should remain the same
  }
];

console.log('Test Scenarios:');
testScenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}`);
  console.log(`   Description: ${scenario.description}`);
  console.log(`   Initial: (${scenario.initialSelection.start.row},${scenario.initialSelection.start.col}) to (${scenario.initialSelection.end.row},${scenario.initialSelection.end.col})`);
  console.log(`   Expected: (${scenario.expectedResult.start.row},${scenario.expectedResult.start.col}) to (${scenario.expectedResult.end.row},${scenario.expectedResult.end.col})`);
});

console.log('\nTo test manually:');
console.log('1. Open the editor application');
console.log('2. Create a table with the structure shown in table.example.html');
console.log('3. Try the selection scenarios above');
console.log('4. Verify that selections always form rectangles and include full merged cells');

// Instructions for manual testing
console.log('\nManual Testing Steps:');
console.log('- Drag to select cells A1, B1, C1');
console.log('- Extend selection down to row 2 (which has the A2-C2 merged cell)');
console.log('- Verify that A1, B1, C1 remain selected (they should not get unselected)');
console.log('- The selection should expand to include the entire A2-C2 merged cell');
