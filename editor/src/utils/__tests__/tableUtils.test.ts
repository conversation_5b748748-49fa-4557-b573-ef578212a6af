import { describe, expect, it } from "vitest";
import type { TableCell } from "@/types/table";
import {
	cellToLogicalPosition,
	createLogicalGrid,
	expandSelectionForMergedCells,
	isCellSelectedInLogicalGrid,
	logicalPositionToCell,
} from "../tableUtils";

describe("tableUtils", () => {
	// Create a test table with colspan and rowspan similar to the example
	const createTestTable = (): TableCell[][] => [
		// Row 0: [cell(1,1), cell(1,1), cell(1,1), cell(1,1), cell(1,1)]
		[
			{
				content: "A1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "E1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
		// Row 1: [cell(3,1), cell(1,1), cell(1,1)]
		[
			{
				content: "A2-C2",
				colspan: 3,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "E2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
		// Row 2: [cell(1,1), cell(1,1), cell(2,1), cell(1,2)]
		[
			{
				content: "A3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C3-D3",
				colspan: 2,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "E3-E4",
				colspan: 1,
				rowspan: 2,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
		// Row 3: [cell(1,1), cell(1,1), cell(1,1), cell(1,1)]
		[
			{
				content: "A4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
	];

	describe("createLogicalGrid", () => {
		it("should create correct logical grid for table with colspan and rowspan", () => {
			const cells = createTestTable();
			const grid = createLogicalGrid(cells, 4, 5);

			// Check that the grid correctly maps logical positions to cell references
			expect(grid[0][0]).toEqual({ rowIndex: 0, colIndex: 0 }); // A1
			expect(grid[0][1]).toEqual({ rowIndex: 0, colIndex: 1 }); // B1
			expect(grid[0][2]).toEqual({ rowIndex: 0, colIndex: 2 }); // C1

			// Row 1 has a colspan=3 cell
			expect(grid[1][0]).toEqual({ rowIndex: 1, colIndex: 0 }); // A2-C2
			expect(grid[1][1]).toEqual({ rowIndex: 1, colIndex: 0 }); // A2-C2 (same cell)
			expect(grid[1][2]).toEqual({ rowIndex: 1, colIndex: 0 }); // A2-C2 (same cell)
			expect(grid[1][3]).toEqual({ rowIndex: 1, colIndex: 1 }); // D2

			// Row 2 has rowspan=2 cell in column E
			expect(grid[2][4]).toEqual({ rowIndex: 2, colIndex: 3 }); // E3-E4
			expect(grid[3][4]).toEqual({ rowIndex: 2, colIndex: 3 }); // E3-E4 (same cell)
		});
	});

	describe("cellToLogicalPosition", () => {
		it("should convert cell array indices to logical positions", () => {
			const cells = createTestTable();

			// First row cells
			expect(cellToLogicalPosition(cells, 0, 0)).toEqual({
				logicalRow: 0,
				logicalCol: 0,
			});
			expect(cellToLogicalPosition(cells, 0, 1)).toEqual({
				logicalRow: 0,
				logicalCol: 1,
			});

			// Second row with colspan
			expect(cellToLogicalPosition(cells, 1, 0)).toEqual({
				logicalRow: 1,
				logicalCol: 0,
			}); // A2-C2 starts at col 0
			expect(cellToLogicalPosition(cells, 1, 1)).toEqual({
				logicalRow: 1,
				logicalCol: 3,
			}); // D2 starts at col 3
		});
	});

	describe("isCellSelectedInLogicalGrid", () => {
		it("should correctly identify selected cells in rectangular selection", () => {
			const cells = createTestTable();

			// Select logical rectangle from (0,0) to (1,2) - should include A1, B1, C1, and the colspan cell A2-C2
			const selection = {
				start: { row: 0, col: 0 },
				end: { row: 1, col: 2 },
			};

			// These cells should be selected
			expect(isCellSelectedInLogicalGrid(cells, 0, 0, selection)).toBe(true); // A1
			expect(isCellSelectedInLogicalGrid(cells, 0, 1, selection)).toBe(true); // B1
			expect(isCellSelectedInLogicalGrid(cells, 0, 2, selection)).toBe(true); // C1
			expect(isCellSelectedInLogicalGrid(cells, 1, 0, selection)).toBe(true); // A2-C2 (overlaps)

			// These cells should not be selected
			expect(isCellSelectedInLogicalGrid(cells, 0, 3, selection)).toBe(false); // D1
			expect(isCellSelectedInLogicalGrid(cells, 1, 1, selection)).toBe(false); // D2
		});
	});

	describe("logicalPositionToCell", () => {
		it("should convert logical positions back to cell array indices", () => {
			const cells = createTestTable();

			// Test conversion back to cell indices
			expect(logicalPositionToCell(cells, 0, 0, 4, 5)).toEqual({
				rowIndex: 0,
				colIndex: 0,
			});
			expect(logicalPositionToCell(cells, 1, 1, 4, 5)).toEqual({
				rowIndex: 1,
				colIndex: 0,
			}); // Points to colspan cell
			expect(logicalPositionToCell(cells, 2, 4, 4, 5)).toEqual({
				rowIndex: 2,
				colIndex: 3,
			}); // Points to rowspan cell
		});
	});

	describe("expandSelectionForMergedCells", () => {
		it("should expand selection to include full merged cells", () => {
			const cells = createTestTable();

			// Test case: Select from A1 to B2, should expand to include the full A2-C2 cell
			const selection = {
				start: { row: 0, col: 0 }, // A1
				end: { row: 1, col: 1 }, // B2 (which is part of A2-C2 colspan cell)
			};

			const expanded = expandSelectionForMergedCells(cells, selection, 4, 5);

			// Should expand to include the full A2-C2 cell
			expect(expanded.start).toEqual({ row: 0, col: 0 }); // A1
			expect(expanded.end).toEqual({ row: 1, col: 2 }); // C2 (end of colspan cell)
		});

		it("should expand selection for rowspan cells", () => {
			const cells = createTestTable();

			// Test case: Select from D3 to E3, should expand to include the full C3-D3 and E3-E4 cells
			const selection = {
				start: { row: 2, col: 3 }, // D3 (part of C3-D3 colspan cell)
				end: { row: 2, col: 4 }, // E3 (which is part of E3-E4 rowspan cell)
			};

			const expanded = expandSelectionForMergedCells(cells, selection, 4, 5);

			// Should expand to include both the C3-D3 colspan cell and the E3-E4 rowspan cell
			expect(expanded.start).toEqual({ row: 2, col: 2 }); // C3 (start of C3-D3 cell)
			expect(expanded.end).toEqual({ row: 3, col: 4 }); // E4 (end of rowspan cell)
		});

		it("should handle complex selections with multiple merged cells", () => {
			const cells = createTestTable();

			// Test case: Select from A2 to D3, should expand to include both merged cells
			const selection = {
				start: { row: 1, col: 0 }, // A2 (start of A2-C2 colspan)
				end: { row: 2, col: 3 }, // D3 (end of C3-D3 colspan)
			};

			const expanded = expandSelectionForMergedCells(cells, selection, 4, 5);

			// Should include both merged cells fully
			expect(expanded.start).toEqual({ row: 1, col: 0 }); // A2
			expect(expanded.end).toEqual({ row: 2, col: 3 }); // D3
		});

		it("should not change selection if no merged cells are involved", () => {
			const cells = createTestTable();

			// Test case: Select only single cells in first row
			const selection = {
				start: { row: 0, col: 0 }, // A1
				end: { row: 0, col: 2 }, // C1
			};

			const expanded = expandSelectionForMergedCells(cells, selection, 4, 5);

			// Should remain unchanged
			expect(expanded).toEqual(selection);
		});

		it("should handle simple rowspan case correctly", () => {
			// Create a simpler table to isolate rowspan issues
			const simpleRowspanTable: TableCell[][] = [
				// Row 0: [A1, B1, C1]
				[
					{ content: 'A1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				// Row 1: [A2, B2-B3(rowspan=2)]
				[
					{ content: 'A2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B2-B3', colspan: 1, rowspan: 2, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				// Row 2: [A3, C3] (B3 is occupied by rowspan)
				[
					{ content: 'A3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				]
			];

			// Test selecting from B1 to B2 (should expand to include full rowspan)
			const selection = {
				start: { row: 0, col: 1 }, // B1
				end: { row: 1, col: 1 }, // B2 (part of B2-B3 rowspan)
			};

			const expanded = expandSelectionForMergedCells(simpleRowspanTable, selection, 3, 3);

			// Should expand to include the full B2-B3 rowspan cell
			expect(expanded.start).toEqual({ row: 0, col: 1 }); // B1
			expect(expanded.end).toEqual({ row: 2, col: 1 }); // B3 (end of rowspan)
		});

		it("should debug logical grid for rowspan table", () => {
			// Create the same table structure as above
			const simpleRowspanTable: TableCell[][] = [
				[
					{ content: 'A1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				[
					{ content: 'A2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B2-B3', colspan: 1, rowspan: 2, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				[
					{ content: 'A3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				]
			];

			const grid = createLogicalGrid(simpleRowspanTable, 3, 3);

			// Check logical grid structure
			// Row 0: [A1, B1, C1]
			expect(grid[0][0]).toEqual({ rowIndex: 0, colIndex: 0 }); // A1
			expect(grid[0][1]).toEqual({ rowIndex: 0, colIndex: 1 }); // B1
			expect(grid[0][2]).toEqual({ rowIndex: 0, colIndex: 2 }); // C1

			// Row 1: [A2, B2-B3, null] - C2 doesn't exist because B2-B3 spans
			expect(grid[1][0]).toEqual({ rowIndex: 1, colIndex: 0 }); // A2
			expect(grid[1][1]).toEqual({ rowIndex: 1, colIndex: 1 }); // B2-B3
			expect(grid[1][2]).toBeNull(); // No C2 cell

			// Row 2: [A3, B2-B3, C3] - B3 is occupied by rowspan
			expect(grid[2][0]).toEqual({ rowIndex: 2, colIndex: 0 }); // A3
			expect(grid[2][1]).toEqual({ rowIndex: 1, colIndex: 1 }); // B2-B3 (same cell)
			expect(grid[2][2]).toEqual({ rowIndex: 2, colIndex: 1 }); // C3

			// Test cell to logical position conversion
			expect(cellToLogicalPosition(simpleRowspanTable, 0, 0)).toEqual({ logicalRow: 0, logicalCol: 0 }); // A1
			expect(cellToLogicalPosition(simpleRowspanTable, 0, 1)).toEqual({ logicalRow: 0, logicalCol: 1 }); // B1
			expect(cellToLogicalPosition(simpleRowspanTable, 1, 1)).toEqual({ logicalRow: 1, logicalCol: 1 }); // B2-B3
			expect(cellToLogicalPosition(simpleRowspanTable, 2, 1)).toEqual({ logicalRow: 2, logicalCol: 2 }); // C3 should be at logical col 2
		});

		it("should handle the original user issue: selecting across rowspan should maintain rectangular selection", () => {
			// Test the specific scenario mentioned by the user
			const complexTable: TableCell[][] = [
				// Row 0: [A1, B1, C1, D1, E1]
				[
					{ content: 'A1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'D1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'E1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				// Row 1: [A2, B2, C2, D2-D3(rowspan=2), E2]
				[
					{ content: 'A2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'D2-D3', colspan: 1, rowspan: 2, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'E2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				// Row 2: [A3, B3, C3, E3] (D3 occupied by rowspan)
				[
					{ content: 'A3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'E3', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				]
			];

			// Scenario: Select C1, D1, E1 in first row, then expand to second row
			// This should maintain the selection of C1, D1, E1 and expand to include the rowspan cell
			// Expand to include row 2 (which has the D2-D3 rowspan cell)
			const expandedSelection = {
				start: { row: 0, col: 2 }, // C1
				end: { row: 1, col: 4 }, // E2
			};

			const result = expandSelectionForMergedCells(complexTable, expandedSelection, 3, 5);

			// The selection should expand to include the full D2-D3 rowspan cell
			expect(result.start).toEqual({ row: 0, col: 2 }); // C1 (unchanged)
			expect(result.end).toEqual({ row: 2, col: 4 }); // Should expand to row 2 to include full D2-D3 cell

			// Verify that all originally selected cells would still be selected
			// C1, D1, E1 should remain selected
			expect(isCellSelectedInLogicalGrid(complexTable, 0, 2, result)).toBe(true); // C1
			expect(isCellSelectedInLogicalGrid(complexTable, 0, 3, result)).toBe(true); // D1
			expect(isCellSelectedInLogicalGrid(complexTable, 0, 4, result)).toBe(true); // E1
		});

		it("should handle selection directions (bottom-to-top, right-to-left)", () => {
			const cells = createTestTable();

			// Test bottom-to-top selection
			const bottomToTop = {
				start: { row: 2, col: 2 }, // C3
				end: { row: 0, col: 0 }, // A1
			};

			const expandedBottomToTop = expandSelectionForMergedCells(cells, bottomToTop, 4, 5);

			// Should preserve the bottom-to-top direction
			expect(expandedBottomToTop.start.row).toBeGreaterThanOrEqual(expandedBottomToTop.end.row);
			expect(expandedBottomToTop.start.col).toBeGreaterThanOrEqual(expandedBottomToTop.end.col);

			// Test right-to-left selection
			const rightToLeft = {
				start: { row: 0, col: 4 }, // E1
				end: { row: 0, col: 0 }, // A1
			};

			const expandedRightToLeft = expandSelectionForMergedCells(cells, rightToLeft, 4, 5);

			// Should preserve the right-to-left direction
			expect(expandedRightToLeft.start.col).toBeGreaterThanOrEqual(expandedRightToLeft.end.col);

			// Test top-left to bottom-right (normal direction)
			const normalDirection = {
				start: { row: 0, col: 0 }, // A1
				end: { row: 2, col: 2 }, // C3
			};

			const expandedNormal = expandSelectionForMergedCells(cells, normalDirection, 4, 5);

			// Should preserve the normal direction
			expect(expandedNormal.start.row).toBeLessThanOrEqual(expandedNormal.end.row);
			expect(expandedNormal.start.col).toBeLessThanOrEqual(expandedNormal.end.col);
		});

		it("should not crash when clicking cells after colspan", () => {
			// Create a table that might cause crashes
			const problematicTable: TableCell[][] = [
				[
					{ content: 'A1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B1-C1', colspan: 2, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'D1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				[
					{ content: 'A2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'D2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				]
			];

			// These should not crash
			expect(() => cellToLogicalPosition(problematicTable, 0, 0)).not.toThrow(); // A1
			expect(() => cellToLogicalPosition(problematicTable, 0, 1)).not.toThrow(); // B1-C1
			expect(() => cellToLogicalPosition(problematicTable, 0, 2)).not.toThrow(); // D1
			expect(() => cellToLogicalPosition(problematicTable, 1, 0)).not.toThrow(); // A2
			expect(() => cellToLogicalPosition(problematicTable, 1, 1)).not.toThrow(); // B2
			expect(() => cellToLogicalPosition(problematicTable, 1, 2)).not.toThrow(); // C2
			expect(() => cellToLogicalPosition(problematicTable, 1, 3)).not.toThrow(); // D2

			// Test the actual logical positions
			expect(cellToLogicalPosition(problematicTable, 0, 2)).toEqual({ logicalRow: 0, logicalCol: 3 }); // D1 should be at logical col 3
			expect(cellToLogicalPosition(problematicTable, 1, 3)).toEqual({ logicalRow: 1, logicalCol: 3 }); // D2 should be at logical col 3
		});

		it("should handle single cell selection after colspan without crashing", () => {
			// Test the specific scenario that causes crashes
			const crashTable: TableCell[][] = [
				[
					{ content: 'A1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B1-D1', colspan: 3, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'E1', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				],
				[
					{ content: 'A2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'B2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'C2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'D2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } },
					{ content: 'E2', colspan: 1, rowspan: 1, backgroundColor: null, borderWidths: { top: 1, right: 1, bottom: 1, left: 1 } }
				]
			];

			// Test single cell selections that might crash
			expect(() => {
				const pos = cellToLogicalPosition(crashTable, 0, 2); // E1 (after colspan)
				const selection = { start: { row: pos.logicalRow, col: pos.logicalCol }, end: { row: pos.logicalRow, col: pos.logicalCol } };
				expandSelectionForMergedCells(crashTable, selection, 2, 5);
			}).not.toThrow();

			expect(() => {
				const pos = cellToLogicalPosition(crashTable, 1, 4); // E2 (after multiple cells)
				const selection = { start: { row: pos.logicalRow, col: pos.logicalCol }, end: { row: pos.logicalRow, col: pos.logicalCol } };
				expandSelectionForMergedCells(crashTable, selection, 2, 5);
			}).not.toThrow();

			// Test that isCellSelectedInLogicalGrid doesn't crash
			expect(() => {
				const selection = { start: { row: 0, col: 4 }, end: { row: 0, col: 4 } }; // E1
				isCellSelectedInLogicalGrid(crashTable, 0, 2, selection);
			}).not.toThrow();

			expect(() => {
				const selection = { start: { row: 1, col: 4 }, end: { row: 1, col: 4 } }; // E2
				isCellSelectedInLogicalGrid(crashTable, 1, 4, selection);
			}).not.toThrow();
		});
	});
});
